<script setup>
import {ScheduleXCalendar} from '@schedule-x/vue'
import {createCalendar, createViewMonthGrid, createViewWeek,} from '@schedule-x/calendar'
import '@schedule-x/theme-default/dist/index.css'
import {createEventsServicePlugin} from '@schedule-x/events-service'
import {createCalendarControlsPlugin} from '@schedule-x/calendar-controls'
import {useRoute, useRouter} from "vue-router";
import {onMounted, ref, watch} from "vue";
import createApiService from "@/services/api.js";
import {format, startOfISOWeek, lastDayOfISOWeek} from "date-fns";
import {definePage} from "unplugin-vue-router/runtime";
import {nl} from "date-fns/locale";
import ProjectViewDialog from "@/components/project/ProjectViewDialog.vue";
import ProjectEditDialog from "@/components/project/ProjectEditDialog.vue";
import GoogleEventDialog from "@/components/planning/GoogleEventDialog.vue";

const route = useRoute();
const router = useRouter();
const api = createApiService(router);
const eventsServicePlugin = createEventsServicePlugin();
const calendarControlsPlugin = createCalendarControlsPlugin()
const date = ref(route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd'));
const calenderView = ref(!route.hash || route.hash === '#week' ? 'week' : 'month-grid');
const showProjectViewDialog = ref(false);
const showEditProjectDialog = ref(false);
const showGoogleEventDialog = ref(false);
const selectedProject = ref(null);
const projectToEdit = ref(null);
const selectedGoogleEvent = ref(null);
const calendarApp = createCalendar({
  selectedDate: date.value,
  locale: 'nl-NL',
  showWeekNumbers: true,
  defaultView: calenderView.value,
  views: [
    createViewWeek(),
    createViewMonthGrid(),
  ],
  monthGridOptions: {
    nEventsPerDay: 100,
  },
  weekOptions: {
    eventOverlap: true,
    gridHeight: 1500,
  },
  callbacks: {
    onEventClick(calendarEvent) {
      showEventDetails(calendarEvent)
    },
  },
}, [eventsServicePlugin, calendarControlsPlugin])
const loading = ref(false);
const events = ref([]);

definePage({
  name: "/calendar",
  meta: {
    title: 'Kalender',
  },
})

onMounted(() => {
  updateEvents()
});

const showEventDetails = (event) => {
  const found = events.value.find(item => item.project && item.project.id === event.id);

  if (found?.project) {
    selectedProject.value = found.project;
    showProjectViewDialog.value = true;
  } else {
    selectedGoogleEvent.value = event;
    showGoogleEventDialog.value = true;
  }
}

const saveChanges = async () => {
  if (!selectedProject.value) return;

  loading.value = true;
  try {
    const savePromises = selectedProject.value.events.map(event =>
      api.post(`eventUpdate?id=${event.id}`, JSON.stringify(event))
    );
    await Promise.all(savePromises);

    showProjectViewDialog.value = false;

    updateEvents();
  } catch (error) {
    console.error('Error saving changes:', error);
    loading.value = false;
  }
};

const updateEvents = async () => {
  loading.value = true;

  try {
    const requestParams = new URLSearchParams({
      view: (calenderView.value === 'week' ? 'week' : 'month'),
      date: date.value,
    }).toString();

    const [eventsResponse, availabilityResponse] = await Promise.all([
      api.get(`eventList?${requestParams}`),
      api.get(`employeeAvailabilityRange?${requestParams}`),
    ]);

    events.value = eventsResponse.data.data;
    const availabilityEvents = availabilityResponse.data.data;

    const regularEvents = events.value.map(event => ({
      id: event.project?.id ?? event.event_id,
      title: event.title,
      description: event.description,
      location: event.project?.address ?? event.location,
      people: event.attendees.map(attendee => (
        attendee.employee?.name ?? attendee.email
      )),
      start: event.start,
      end: event.end,
      _options: {
        additionalClasses: [(event.project ? 'project-status-' + event.project.status : 'external')],
      },
    }));

    const formattedAvailabilityEvents = availabilityEvents.map(event => ({
      id: event.id,
      title: event.title,
      description: event.description,
      start: event.start,
      end: event.end,
      _options: {
        additionalClasses: ['employee-availability'],
      },
    }));

    eventsServicePlugin.set([...regularEvents, ...formattedAvailabilityEvents]);
  } catch (error) {
    console.error('Error updating events:', error);
  } finally {
    loading.value = false;
  }
}

const getTitle = () => {
  if (calenderView.value === 'week') {
    const startMonth = format(startOfISOWeek(date.value), 'MMMM', {locale: nl});
    const endMonth = format(lastDayOfISOWeek(date.value), 'MMMM', {locale: nl});
    const startYear = format(startOfISOWeek(date.value), 'yyyy', {locale: nl});
    const endYear = format(lastDayOfISOWeek(date.value), 'yyyy', {locale: nl});

    const weekNumber = format(date.value, 'w', {locale: nl});

    return (startMonth !== endMonth ? startMonth + (startYear !== endYear ? ' ' + startYear : '') + ' - ' + endMonth : startMonth) + ' ' + endYear + ': week ' + weekNumber;
  }
  else {
    return format(date.value, 'MMMM yyyy', {locale: nl});
  }
}

const openEdit = (project) => {
  showProjectViewDialog.value = false;
  projectToEdit.value = project;
  showEditProjectDialog.value = true;
};

const onProjectSaved = () => {
  showEditProjectDialog.value = false;
  updateEvents();
};

watch(route, () => {
  date.value = route.params.date?.length ? route.params.date : format(new Date(), 'yyyy-MM-dd');
  calenderView.value = (!route.hash || route.hash === '#week') ? 'week' : 'month-grid';

  calendarControlsPlugin.setView(calenderView.value)
  calendarControlsPlugin.setDate(date.value)

  updateEvents();
});
</script>

<template>
  <v-card-title class="space-between pt-4 pb-4">
    <span>
      {{ $filters.ucFirst(getTitle()) }}
      <v-icon
        icon="mdi-refresh"
        color="primary"
        size="small"
        :disabled="loading"
        @click="updateEvents"
      />
    </span>
    <v-btn
      prepend-icon="mdi-clipboard-account-outline"
      color="primary"
      :to="{ name: '/planning', params: { date: date } }"
    >
      Planning
    </v-btn>
  </v-card-title>
  <v-progress-linear
    :active="loading"
    indeterminate
    color="primary"
  />
  <v-divider v-if="calenderView === 'week'" />
  <ScheduleXCalendar :calendar-app="calendarApp" />

  <project-view-dialog
    v-model="showProjectViewDialog"
    :project="selectedProject"
    :loading="loading"
    @edit="openEdit"
    @save="saveChanges"
  />
  <project-edit-dialog
    v-model="showEditProjectDialog"
    :project-data="projectToEdit"
    @saved="onProjectSaved"
  />
  <google-event-dialog
    v-model="showGoogleEventDialog"
    :event="selectedGoogleEvent"
  />
</template>

<style lang="scss">
.sx__calendar-header {
  display: none !important;
}

.sx__calendar {
  border: none;
}

.sx__time-grid-event {
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-right: none !important;
  border-bottom: none !important;
}

.sx__time-grid-event:hover {
  min-width: 200px !important;
  z-index: 9999;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.sx__month-grid-event,
.sx__date-grid-day .sx__date-grid-event-text,
.sx__time-grid-event-inner {
  padding: 3px;
}

.sx__date-grid-day {
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

:root {
  --sx-color-primary: rgb(var(--v-theme-primary));
  --sx-color-primary-container: white;
  --sx-color-on-primary-container: rgb(var(--v-theme-on-surface));
  --sx-color-outline: rgb(var(--v-theme-on-surface));
  --sx-color-outline-variant: rgba(var(--v-border-color), var(--v-border-opacity));
  --sx-spacing-padding1: 0;
  --sx-rounding-extra-small: 0;
  --sx-rounding-small: 0;
}

.project-status-blast_sand {
  border-inline-start-color: rgb(var(--v-theme-indufastYellow)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastYellow), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-blast_sand {
  background-color: rgba(var(--v-theme-indufastOrange), var(--v-light-opacity)) !important;
}

.project-status-planning {
  border-inline-start-color: rgb(var(--v-theme-indufastBlue)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastBlue), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-planning {
  background-color: rgba(var(--v-theme-indufastBlue), var(--v-light-opacity)) !important;
}

.project-status-incomplete {
  border-inline-start-color: rgb(var(--v-theme-indufastRed)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastRed), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-incomplete {
  background-color: rgba(var(--v-theme-indufastRed), var(--v-light-opacity)) !important;
}

.project-status-weather_dependent {
  border-inline-start-color: rgb(var(--v-theme-indufastOrange)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastOrange), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-weather_dependent {
  background-color: rgba(var(--v-theme-indufastOrange), var(--v-light-opacity)) !important;
}

.project-status-complete {
  border-inline-start-color: rgb(var(--v-theme-indufastGreen)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastGreen), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-complete {
  background-color: rgba(var(--v-theme-indufastGreen), var(--v-light-opacity)) !important;
}

.project-status-unplanned {
  border-inline-start-color: rgb(var(--v-theme-indufastPurple)) !important;
  .sx__time-grid-event-inner {
    background-color: rgba(var(--v-theme-indufastPurple), var(--v-light-opacity)) !important;
  }
}
.sx__month-grid-event.project-status-unplanned {
  background-color: rgba(var(--v-theme-indufastPurple), var(--v-light-opacity)) !important;
}
</style>
