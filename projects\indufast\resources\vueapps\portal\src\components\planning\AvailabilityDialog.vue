<script setup>
import { computed } from 'vue';
import { employeeAvailability } from '@/helpers/constants.js';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  availabilityData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

const closeDialog = () => {
  emit('update:modelValue', false);
};

// Compute the total number of available employees
const totalAvailable = computed(() => {
  if (!props.availabilityData.availability_counts) return 0;

  const counts = props.availabilityData.availability_counts;
  return (counts.available || 0) +
         (counts.available_morning || 0) +
         (counts.available_afternoon || 0) +
         (counts.available_morning_afternoon || 0);
});

// Group employees by their availability status
const groupedEmployees = computed(() => {
  if (!props.availabilityData.employee_details) return {};

  const groups = {};

  // Initialize groups for all availability types
  employeeAvailability.forEach(type => {
    groups[type.value] = {
      ...type,
      employees: []
    };
  });

  // Group employees by their availability
  props.availabilityData.employee_details.forEach(employee => {
    if (groups[employee.availability]) {
      groups[employee.availability].employees.push(employee);
    }
  });

  // Filter out empty groups
  return Object.values(groups).filter(group => group.employees.length > 0);
});
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    width="600px"
    scrollable
    @update:model-value="closeDialog"
  >
    <v-card>
      <v-toolbar color="primary">
        <v-toolbar-title>
          Beschikbaarheid {{ availabilityData.date ? $filters.formatDate(availabilityData.date) : '' }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            icon="mdi-close"
            @click="closeDialog"
          />
        </v-toolbar-items>
      </v-toolbar>
      <v-card-text class="pa-2">
        <v-list
          density="compact"
          class="pt-0"
        >
          <v-list-item
            prepend-icon="mdi-calendar-today"
            slim
          >
            <strong>{{ totalAvailable }} medewerkers beschikbaar</strong>
          </v-list-item>

          <template
            v-for="group in groupedEmployees"
            :key="group.value"
          >
            <v-divider class="my-2" />
            <v-list-item
              :prepend-icon="group.icon"
              slim
            >
              <v-list-item-title>
                <strong :class="`text-${group.color}`">
                  {{ group.title }} ({{ group.employees.length }})
                </strong>
              </v-list-item-title>
            </v-list-item>
            <v-list-item class="pl-8">
              <div class="d-flex flex-wrap ga-2">
                <v-chip
                  v-for="employee in group.employees"
                  :key="employee.name"
                  size="small"
                  variant="outlined"
                  :color="group.color"
                >
                  {{ employee.name }}
                </v-chip>
              </div>
            </v-list-item>
          </template>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.text-indufastGreen {
  color: rgb(var(--v-theme-indufastGreen));
}

.text-indufastYellow {
  color: rgb(var(--v-theme-indufastYellow));
}

.text-indufastBlue {
  color: rgb(var(--v-theme-indufastBlue));
}

.text-indufastRed {
  color: rgb(var(--v-theme-indufastRed));
}

.text-indufastOrange {
  color: rgb(var(--v-theme-indufastOrange));
}
</style>
