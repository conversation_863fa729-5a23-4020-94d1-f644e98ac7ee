<?php

  use classes\ApiResponse;
  use domain\project\service\ProjectUpdater;

  trait IndufastEmployeeAvailabilityApiTrait {
    use PropertyCastTrait;

    public function executeEmployeeAvailabilityList(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
      ];
      $data = $this->validateRequest($_GET, $rules)->getValidatedData();

      $result = [];
      try {
        $employees = IndufastEmployee::find_all_by(['active' => true]);
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $data['date']);

        foreach ($employees as $employee) {
          $events = $eventsByEmployee[$employee->id] ?? [];
          $availability = $employee->calculateAvailabilityFromEvents($events, $data['date'], true);

          $result[] = [
            'employee'     => $employee,
            'availability' => $availability,
            'events'       => $events,
          ];
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }

    public function executeEmployeeAvailabilityUpdate(): void {
      try {
        // Updates employee availability and project validity
        ProjectUpdater::updateProjectValidity();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }
    }

    public function executeEmployeeAvailabilityForProject(): void {
      $rules = [
        'employee_id' => 'required|integer',
        'project_id' => 'required|integer',
        'exclude_event_id' => 'integer',
      ];
      $data = $this->validateRequest($_GET, $rules)->getValidatedData();

      try {
        $project = IndufastProject::find_by_id($data['project_id']);
        if (!$project) {
          throw new \Exception('Project not found');
        }

        $employee = IndufastEmployee::find_by_id($data['employee_id']);
        if (!$employee) {
          throw new \Exception('Employee not found');
        }

        $events = [];
        foreach ($project->events() as $event) {
          if ($data['exclude_event_id'] && $event->id == $data['exclude_event_id']) {
            continue;
          }

          $date = date('Y-m-d', strtotime($event->start));
          $availability = $employee->getAvailability($date, eventToIgnore: $event);
          $events[] = [
            'event' => $event,
            'availability' => $availability,
            'conflict' => $employee->hasConflict($event, $availability),
          ];
        }

      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $events);
    }

    public function executeEmployeeAvailabilityRange(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
        'view' => 'required|string|in:week,month',
      ];
      $validation = $this->validateRequest($_GET, $rules);
      $data = $validation->getValidatedData();

      ['start' => $start, 'end' => $end] = $this->getStartAndEndDate($data['date'], $data['view']);

      $result = [];
      try {
        $employees = IndufastEmployee::find_all_by(['active' => true]);
        // Get all calendar events for all employees in the specified date range
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $start, $end);

        // Generate availability for each day in the range
        $currentDate = new DateTime($start);
        $endDate = new DateTime($end);

        while ($currentDate <= $endDate) {
          $dateString = $currentDate->format('Y-m-d');

          // Collect availability data for this day
          $availabilityCounts = [
            'available' => 0,
            'available_morning' => 0,
            'available_afternoon' => 0,
            'available_morning_afternoon' => 0,
            'not_available' => 0,
            'not_available_error' => 0,
          ];

          $employeeDetails = [];

          foreach ($employees as $employee) {
            $employeeEvents = $eventsByEmployee[$employee->id] ?? [];

            // Filter events for the current date
            $dayEvents = array_filter($employeeEvents, fn ($event) => date('Y-m-d', strtotime($event->start->dateTime ?? $event->start->date)) === $dateString);

            $availability = $employee->calculateAvailabilityFromEvents($dayEvents, $dateString);

            // Count availability statuses
            if (isset($availabilityCounts[$availability])) {
              $availabilityCounts[$availability]++;
            }

            // Store employee details for description
            $employeeDetails[] = [
              'name' => $employee->name ?? $employee->email ?? 'Unknown',
              'availability' => $availability,
            ];
          }

          // Build the event object for this day
          $event = $this->buildAvailabilityEvent($dateString, $availabilityCounts, $employeeDetails);
          $result[] = $event;

          $currentDate->modify('+1 day');
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }

    private function buildAvailabilityEvent(string $date, array $counts, array $employeeDetails): array {
      // Define availability types and their corresponding labels and descriptions
      $availabilityTypes = [
        'available' => [
          'description_prefix' => 'Hele dag beschikbaar',
        ],
        'available_morning' => [
          'description_prefix' => 'Ochtend beschikbaar',
        ],
        'available_afternoon' => [
          'description_prefix' => 'Middag beschikbaar',
        ],
        'available_morning_afternoon' => [
          'description_prefix' => 'Ochtend + Middag beschikbaar',
        ],
        'not_available' => [
          'description_prefix' => 'Niet beschikbaar',
        ],
        'not_available_error' => [
          'description_prefix' => 'Fout bij ophalen beschikbaarheid',
        ],
      ];

      // Calculate total available for the title
      $totalAvailable = 0;
      foreach (['available', 'available_morning', 'available_afternoon', 'available_morning_afternoon'] as $key) {
        if (isset($counts[$key]) && $counts[$key] > 0) {
          $totalAvailable += $counts[$key];
        }
      }

      // Group employees by availability
      $groupedEmployees = [];
      foreach ($availabilityTypes as $type => $details) {
        $groupedEmployees[$type] = [];
      }

      foreach ($employeeDetails as $emp) {
        if (isset($groupedEmployees[$emp['availability']])) {
          $groupedEmployees[$emp['availability']][] = $emp['name'];
        }
      }

      // Build the description with HTML markup and bullet points
      $descriptionParts = [];
      foreach ($availabilityTypes as $type => $details) {
        if (!empty($groupedEmployees[$type])) {
          $employeeListHtml = "<ul>";
          foreach ($groupedEmployees[$type] as $employeeName) {
            $employeeListHtml .= "<li>{$employeeName}</li>";
          }
          $employeeListHtml .= "</ul>";

          $descriptionParts[] = "<p><b>{$details['description_prefix']}:</b>{$employeeListHtml}</p>";
        }
      }

      return [
        'id' => "availability-{$date}",
        'title' => "$totalAvailable beschikbaar", // Single title directly
        'description' => implode("\n", $descriptionParts),
        'start' => $date,
        'end' => $date,
        'type' => 'availability',
      ];
    }
  }